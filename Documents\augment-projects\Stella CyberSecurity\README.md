# Stella CyberSecurity - Enhanced Web Vulnerability Scanner

A comprehensive PyQt5 desktop application for automated web security vulnerability scanning with AI-powered analysis and visual browser demonstration using Google's Gemini AI and Selenium WebDriver.

## ⚠️ IMPORTANT LEGAL NOTICE

**This tool should ONLY be used for authorized security testing on:**
- Systems you own
- Systems you have explicit written permission to test
- Authorized penetration testing engagements

**Unauthorized security testing is illegal and unethical. Users are solely responsible for ensuring proper authorization before using this tool.**

## Features

### 🔍 Security Testing Capabilities
- **SQL Injection Detection**: Tests for various SQL injection vulnerabilities with form interaction
- **Cross-Site Scripting (XSS)**: Detects reflected XSS vulnerabilities with payload execution
- **Directory Traversal**: Tests for path traversal vulnerabilities
- **SSL/TLS Analysis**: Analyzes SSL/TLS configuration and certificate status
- **Authentication Bypass Testing**: Basic authentication bypass attempts
- **Input Validation Testing**: Tests for improper input handling

### 🤖 AI-Powered Analysis
- **Gemini AI Integration**: Advanced vulnerability analysis and explanations
- **Risk Assessment**: Automated risk scoring for discovered vulnerabilities
- **Remediation Recommendations**: Detailed fix suggestions for each issue
- **Security Posture Assessment**: Overall security evaluation

### 🖥️ Enhanced User Interface
- **Tabbed Interface**: Separate tabs for scan results and visual demonstrations
- **Modern PyQt5 GUI**: Clean, professional interface with grey background
- **Real-time Progress**: Live updates during scanning process
- **Comprehensive Reporting**: Detailed vulnerability reports with timestamps
- **Export Capabilities**: Copy results for external reporting
- **Ethical Use Disclaimer**: Built-in legal and ethical warnings

### 🌐 Browser Integration & Visual Demonstration
- **Headless Browser Automation**: Uses Selenium WebDriver for interactive testing
- **Real-time Screenshots**: Captures before/after screenshots of vulnerability tests
- **Form Interaction**: Automatically finds and tests web forms
- **Visual Evidence**: Provides visual proof of successful vulnerability exploitation
- **Step-by-step Documentation**: Shows exactly how each test is performed
- **Educational Mode**: Demonstrates vulnerability testing techniques visually

## Installation

### Prerequisites
- Python 3.7 or higher
- pip package manager

### Setup Instructions

1. **Clone or download the project files**
2. **Install required dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Get a Gemini AI API Key (Optional but recommended):**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Keep it secure for use in the application

## Usage

### Running the Application
```bash
python stella_security_scanner.py
```

### Using the Scanner

1. **Accept the Ethical Use Disclaimer**
   - Read and accept the legal notice before proceeding

2. **Enter Target URL**
   - Input the URL you want to scan
   - Ensure you have proper authorization to test this target

3. **Enter Gemini AI API Key (Optional)**
   - For enhanced AI-powered analysis and recommendations
   - Leave blank for basic scanning without AI analysis

4. **Start the Scan**
   - Click "Start Security Scan" to begin
   - Monitor real-time progress in the results area

5. **Review Results**
   - View detailed vulnerability findings
   - Read AI-powered analysis and recommendations
   - Use "Clear Results" to reset for a new scan

### Scan Types Performed

The scanner automatically performs the following tests:

1. **SQL Injection Tests**
   - Tests various SQL injection payloads
   - Detects database error messages
   - Identifies potential injection points

2. **XSS Vulnerability Tests**
   - Tests for reflected cross-site scripting
   - Checks payload reflection in responses
   - Identifies potential XSS vectors

3. **Directory Traversal Tests**
   - Tests for path traversal vulnerabilities
   - Attempts to access system files
   - Identifies directory listing exposures

4. **SSL/TLS Security Analysis**
   - Analyzes SSL/TLS configuration
   - Checks for weak ciphers
   - Validates certificate status and expiration

## Output and Reporting

### Scan Results Include:
- **Vulnerability Type**: Classification of each issue found
- **Evidence**: Specific indicators that triggered the detection
- **Affected URLs**: Exact locations where vulnerabilities were found
- **Timestamps**: When each vulnerability was discovered
- **Payloads Used**: The specific test inputs that revealed the issue

### AI Analysis Provides:
- **Risk Assessment**: Critical/High/Medium/Low risk ratings
- **Detailed Explanations**: Technical details about each vulnerability
- **Remediation Steps**: Specific instructions for fixing issues
- **Security Recommendations**: Overall security improvement suggestions

## Technical Details

### Architecture
- **PyQt5 GUI Framework**: Modern desktop interface
- **Threading**: Non-blocking scanning operations
- **HTTP Client**: Robust request handling with proper timeouts
- **SSL Analysis**: Certificate and cipher suite evaluation
- **AI Integration**: Google Gemini API for advanced analysis

### Security Features
- **Rate Limiting**: Prevents overwhelming target servers
- **Timeout Handling**: Prevents hanging on unresponsive targets
- **Error Handling**: Graceful handling of network and API errors
- **Input Validation**: Proper URL validation and sanitization

## Limitations and Considerations

### Scanner Limitations
- **Surface-level Testing**: Performs basic vulnerability detection
- **False Positives**: May report issues that require manual verification
- **Limited Scope**: Does not cover all possible vulnerability types
- **Network Dependent**: Requires stable internet connection

### Recommendations
- **Manual Verification**: Always verify automated findings manually
- **Comprehensive Testing**: Use additional security tools for complete assessment
- **Professional Review**: Consider professional penetration testing for critical systems
- **Regular Updates**: Keep the tool and its dependencies updated

## Troubleshooting

### Common Issues

1. **PyQt5 Installation Problems**
   ```bash
   pip install --upgrade PyQt5
   ```

2. **Gemini AI API Errors**
   - Verify API key is correct
   - Check API quota and billing status
   - Ensure stable internet connection

3. **Network Connectivity Issues**
   - Check firewall settings
   - Verify target URL accessibility
   - Test with a known working website

4. **Permission Errors**
   - Ensure proper file permissions
   - Run with appropriate user privileges
   - Check antivirus software interference

## Contributing

This tool is designed for educational and authorized security testing purposes. Contributions should focus on:
- Improving detection accuracy
- Adding new vulnerability tests
- Enhancing the user interface
- Strengthening ethical use guidelines

## License and Disclaimer

This software is provided for educational and authorized security testing purposes only. Users are solely responsible for ensuring they have proper authorization before testing any systems. The developers assume no liability for misuse of this tool.

## Support

For technical support or questions about authorized use, please refer to the documentation or consult with cybersecurity professionals in your organization.
