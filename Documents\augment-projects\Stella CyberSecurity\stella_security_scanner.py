#!/usr/bin/env python3
"""
Stella CyberSecurity - Web Vulnerability Scanner with AI Analysis
A PyQt5 desktop application for automated web security testing with Gemini AI integration.

IMPORTANT: This tool should only be used for authorized security testing on systems
you own or have explicit permission to test. Unauthorized testing is illegal and unethical.
"""

import sys
import time
import threading
import ssl
import socket
import urllib.parse
import requests
import json
import os
import base64
from datetime import datetime
from io import BytesIO
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QLineEdit, QTextEdit, QPushButton,
                            QProgressBar, QMessageBox, QFrame, QSplitter, QTabWidget,
                            QScrollArea, QGridLayout, QGroupBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import <PERSON><PERSON><PERSON>, Q<PERSON>alette, QColor, QPixmap
import google.generativeai as genai

# Browser automation imports
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.firefox.options import Options as FirefoxOptions
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
    from webdriver_manager.firefox import GeckoDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# Image processing imports
try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class BrowserManager:
    """Manages headless browser operations for visual security testing"""

    def __init__(self):
        self.driver = None
        self.screenshots = []
        self.current_step = ""

    def initialize_browser(self, browser_type="chrome", headless=True):
        """Initialize the browser driver"""
        if not SELENIUM_AVAILABLE:
            raise Exception("Selenium is not available. Please install selenium and webdriver-manager.")

        try:
            if browser_type.lower() == "chrome":
                options = ChromeOptions()
                if headless:
                    options.add_argument("--headless")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-gpu")
                options.add_argument("--window-size=1920,1080")

                self.driver = webdriver.Chrome(
                    service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                    options=options
                )
            else:  # Firefox
                options = FirefoxOptions()
                if headless:
                    options.add_argument("--headless")
                options.add_argument("--width=1920")
                options.add_argument("--height=1080")

                self.driver = webdriver.Firefox(
                    service=webdriver.firefox.service.Service(GeckoDriverManager().install()),
                    options=options
                )

            self.driver.set_page_load_timeout(30)
            return True

        except Exception as e:
            print(f"Failed to initialize browser: {str(e)}")
            return False

    def navigate_to_url(self, url):
        """Navigate to the target URL and capture screenshot"""
        if not self.driver:
            return False

        try:
            self.current_step = f"Navigating to {url}"
            self.driver.get(url)
            time.sleep(2)  # Wait for page load

            # Capture initial screenshot
            screenshot_data = self.capture_screenshot("Initial page load")
            return screenshot_data

        except Exception as e:
            print(f"Failed to navigate to URL: {str(e)}")
            return None

    def find_forms(self):
        """Find all forms on the current page"""
        if not self.driver:
            return []

        try:
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            form_info = []

            for i, form in enumerate(forms):
                inputs = form.find_elements(By.TAG_NAME, "input")
                textareas = form.find_elements(By.TAG_NAME, "textarea")

                form_data = {
                    'index': i,
                    'action': form.get_attribute('action') or 'current_page',
                    'method': form.get_attribute('method') or 'GET',
                    'inputs': [],
                    'element': form
                }

                for input_elem in inputs:
                    input_type = input_elem.get_attribute('type') or 'text'
                    input_name = input_elem.get_attribute('name') or f'input_{len(form_data["inputs"])}'

                    if input_type not in ['submit', 'button', 'hidden']:
                        form_data['inputs'].append({
                            'type': input_type,
                            'name': input_name,
                            'element': input_elem
                        })

                for textarea in textareas:
                    textarea_name = textarea.get_attribute('name') or f'textarea_{len(form_data["inputs"])}'
                    form_data['inputs'].append({
                        'type': 'textarea',
                        'name': textarea_name,
                        'element': textarea
                    })

                if form_data['inputs']:  # Only include forms with inputs
                    form_info.append(form_data)

            return form_info

        except Exception as e:
            print(f"Failed to find forms: {str(e)}")
            return []

    def test_xss_payload(self, form_data, payload):
        """Test XSS payload on a specific form"""
        if not self.driver:
            return None

        try:
            self.current_step = f"Testing XSS payload: {payload[:50]}..."

            # Take before screenshot
            before_screenshot = self.capture_screenshot(f"Before XSS test - {payload[:30]}")

            # Fill form with payload
            for input_data in form_data['inputs']:
                if input_data['type'] in ['text', 'search', 'email', 'textarea']:
                    input_data['element'].clear()
                    input_data['element'].send_keys(payload)
                    break  # Test first suitable input

            # Submit form or trigger event
            try:
                submit_button = form_data['element'].find_element(By.CSS_SELECTOR, 'input[type="submit"], button[type="submit"], button')
                submit_button.click()
            except:
                # If no submit button, try pressing Enter
                form_data['inputs'][0]['element'].send_keys(Keys.RETURN)

            time.sleep(2)  # Wait for response

            # Take after screenshot
            after_screenshot = self.capture_screenshot(f"After XSS test - {payload[:30]}")

            # Check for XSS execution (look for alert dialogs, script execution, etc.)
            page_source = self.driver.page_source

            # Check if payload is reflected
            payload_reflected = payload in page_source

            # Check for potential XSS indicators
            xss_indicators = [
                "alert('XSS')",
                "alert(\"XSS\")",
                "<script>",
                "javascript:",
                "onerror=",
                "onload="
            ]

            xss_detected = any(indicator in page_source for indicator in xss_indicators)

            return {
                'payload': payload,
                'reflected': payload_reflected,
                'xss_detected': xss_detected,
                'before_screenshot': before_screenshot,
                'after_screenshot': after_screenshot,
                'page_source_snippet': page_source[:1000] if payload_reflected else None
            }

        except Exception as e:
            print(f"Failed to test XSS payload: {str(e)}")
            return None

    def test_sql_injection_payload(self, form_data, payload):
        """Test SQL injection payload on a specific form"""
        if not self.driver:
            return None

        try:
            self.current_step = f"Testing SQL injection payload: {payload[:50]}..."

            # Take before screenshot
            before_screenshot = self.capture_screenshot(f"Before SQL injection test - {payload[:30]}")

            # Fill form with payload
            for input_data in form_data['inputs']:
                if input_data['type'] in ['text', 'search', 'email', 'password', 'textarea']:
                    input_data['element'].clear()
                    input_data['element'].send_keys(payload)
                    break  # Test first suitable input

            # Submit form
            try:
                submit_button = form_data['element'].find_element(By.CSS_SELECTOR, 'input[type="submit"], button[type="submit"], button')
                submit_button.click()
            except:
                form_data['inputs'][0]['element'].send_keys(Keys.RETURN)

            time.sleep(2)  # Wait for response

            # Take after screenshot
            after_screenshot = self.capture_screenshot(f"After SQL injection test - {payload[:30]}")

            # Check for SQL error indicators
            page_source = self.driver.page_source.lower()

            sql_error_indicators = [
                "mysql_fetch", "ora-", "microsoft ole db", "odbc sql server",
                "postgresql", "sqlite", "syntax error", "mysql_num_rows",
                "warning: mysql", "error in your sql syntax", "ora-00933",
                "microsoft jet database", "odbc microsoft access"
            ]

            sql_error_detected = any(indicator in page_source for indicator in sql_error_indicators)

            return {
                'payload': payload,
                'sql_error_detected': sql_error_detected,
                'before_screenshot': before_screenshot,
                'after_screenshot': after_screenshot,
                'error_indicators': [indicator for indicator in sql_error_indicators if indicator in page_source]
            }

        except Exception as e:
            print(f"Failed to test SQL injection payload: {str(e)}")
            return None

    def capture_screenshot(self, description=""):
        """Capture a screenshot of the current page"""
        if not self.driver:
            return None

        try:
            # Take screenshot
            screenshot_data = self.driver.get_screenshot_as_base64()

            screenshot_info = {
                'timestamp': datetime.now().isoformat(),
                'description': description,
                'step': self.current_step,
                'data': screenshot_data,
                'url': self.driver.current_url
            }

            self.screenshots.append(screenshot_info)
            return screenshot_info

        except Exception as e:
            print(f"Failed to capture screenshot: {str(e)}")
            return None

    def close_browser(self):
        """Close the browser and clean up"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

class SecurityScanner:
    """Core security scanning functionality with browser integration"""

    def __init__(self, use_browser=True):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Stella-Security-Scanner/1.0 (Authorized Testing)'
        })
        self.vulnerabilities = []
        self.use_browser = use_browser and SELENIUM_AVAILABLE
        self.browser_manager = None

        if self.use_browser:
            self.browser_manager = BrowserManager()

    def initialize_browser_testing(self):
        """Initialize browser for visual testing"""
        if self.use_browser and self.browser_manager:
            return self.browser_manager.initialize_browser()
        return False

    def close_browser(self):
        """Close browser if it was used"""
        if self.browser_manager:
            self.browser_manager.close_browser()

    def test_sql_injection(self, url):
        """Test for SQL injection vulnerabilities with browser integration"""
        payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' OR 'x'='x",
            "1' OR '1'='1' /*"
        ]

        vulnerabilities = []
        browser_results = []

        # Browser-based testing if available
        if self.use_browser and self.browser_manager and self.browser_manager.driver:
            try:
                # Navigate to the target URL
                initial_screenshot = self.browser_manager.navigate_to_url(url)

                # Find forms on the page
                forms = self.browser_manager.find_forms()

                if forms:
                    for form in forms[:2]:  # Test first 2 forms to avoid overwhelming
                        for payload in payloads[:3]:  # Test first 3 payloads per form
                            result = self.browser_manager.test_sql_injection_payload(form, payload)
                            if result and result['sql_error_detected']:
                                vulnerabilities.append({
                                    'type': 'SQL Injection (Browser-detected)',
                                    'payload': payload,
                                    'url': url,
                                    'evidence': f"SQL errors detected: {', '.join(result['error_indicators'])}",
                                    'timestamp': datetime.now().isoformat(),
                                    'form_action': form['action'],
                                    'form_method': form['method'],
                                    'visual_evidence': {
                                        'before_screenshot': result['before_screenshot'],
                                        'after_screenshot': result['after_screenshot']
                                    }
                                })

                            if result:
                                browser_results.append(result)

                            time.sleep(1)  # Rate limiting
            except Exception as e:
                print(f"Browser-based SQL injection testing failed: {str(e)}")

        # Traditional HTTP-based testing
        for payload in payloads:
            try:
                # Test GET parameters
                test_url = f"{url}?id={payload}"
                response = self.session.get(test_url, timeout=10)

                # Look for SQL error indicators
                error_indicators = [
                    "mysql_fetch", "ORA-", "Microsoft OLE DB", "ODBC SQL Server",
                    "PostgreSQL", "SQLite", "syntax error", "mysql_num_rows"
                ]

                for indicator in error_indicators:
                    if indicator.lower() in response.text.lower():
                        vulnerabilities.append({
                            'type': 'SQL Injection (HTTP-detected)',
                            'payload': payload,
                            'url': test_url,
                            'evidence': indicator,
                            'timestamp': datetime.now().isoformat()
                        })
                        break

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                continue

        return vulnerabilities, browser_results

    def test_xss(self, url):
        """Test for Cross-Site Scripting vulnerabilities with browser integration"""
        payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//",
            "<svg onload=alert('XSS')>",
            "\"onmouseover=\"alert('XSS')\""
        ]

        vulnerabilities = []
        browser_results = []

        # Browser-based testing if available
        if self.use_browser and self.browser_manager and self.browser_manager.driver:
            try:
                # Find forms on the page (should already be navigated from SQL injection test)
                forms = self.browser_manager.find_forms()

                if forms:
                    for form in forms[:2]:  # Test first 2 forms
                        for payload in payloads[:3]:  # Test first 3 payloads per form
                            result = self.browser_manager.test_xss_payload(form, payload)
                            if result and (result['reflected'] or result['xss_detected']):
                                vulnerabilities.append({
                                    'type': 'Cross-Site Scripting (Browser-detected)',
                                    'payload': payload,
                                    'url': url,
                                    'evidence': f"Payload reflected: {result['reflected']}, XSS detected: {result['xss_detected']}",
                                    'timestamp': datetime.now().isoformat(),
                                    'form_action': form['action'],
                                    'form_method': form['method'],
                                    'visual_evidence': {
                                        'before_screenshot': result['before_screenshot'],
                                        'after_screenshot': result['after_screenshot']
                                    }
                                })

                            if result:
                                browser_results.append(result)

                            time.sleep(1)  # Rate limiting
            except Exception as e:
                print(f"Browser-based XSS testing failed: {str(e)}")

        # Traditional HTTP-based testing
        for payload in payloads:
            try:
                # Test GET parameters
                test_url = f"{url}?search={urllib.parse.quote(payload)}"
                response = self.session.get(test_url, timeout=10)

                # Check if payload is reflected in response
                if payload in response.text or urllib.parse.quote(payload) in response.text:
                    vulnerabilities.append({
                        'type': 'Cross-Site Scripting (HTTP-detected)',
                        'payload': payload,
                        'url': test_url,
                        'evidence': 'Payload reflected in response',
                        'timestamp': datetime.now().isoformat()
                    })

                time.sleep(0.5)  # Rate limiting

            except Exception:
                continue

        return vulnerabilities, browser_results

    def test_directory_traversal(self, url):
        """Test for directory traversal vulnerabilities"""
        payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd"
        ]

        vulnerabilities = []
        for payload in payloads:
            try:
                test_url = f"{url}?file={payload}"
                response = self.session.get(test_url, timeout=10)

                # Look for file content indicators
                indicators = ["root:", "daemon:", "bin:", "[boot loader]", "127.0.0.1"]

                for indicator in indicators:
                    if indicator in response.text:
                        vulnerabilities.append({
                            'type': 'Directory Traversal',
                            'payload': payload,
                            'url': test_url,
                            'evidence': f'Found indicator: {indicator}',
                            'timestamp': datetime.now().isoformat()
                        })
                        break

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                continue

        return vulnerabilities

    def analyze_ssl_tls(self, url):
        """Analyze SSL/TLS configuration"""
        vulnerabilities = []
        try:
            parsed_url = urllib.parse.urlparse(url)
            hostname = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)

            if parsed_url.scheme == 'https':
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cert = ssock.getpeercert()
                        cipher = ssock.cipher()

                        # Check for weak ciphers
                        weak_ciphers = ['RC4', 'DES', 'MD5']
                        if any(weak in cipher[0] for weak in weak_ciphers):
                            vulnerabilities.append({
                                'type': 'Weak SSL/TLS Cipher',
                                'evidence': f'Weak cipher detected: {cipher[0]}',
                                'url': url,
                                'timestamp': datetime.now().isoformat()
                            })

                        # Check certificate expiration
                        not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                        days_until_expiry = (not_after - datetime.now()).days

                        if days_until_expiry < 30:
                            vulnerabilities.append({
                                'type': 'SSL Certificate Expiring Soon',
                                'evidence': f'Certificate expires in {days_until_expiry} days',
                                'url': url,
                                'timestamp': datetime.now().isoformat()
                            })

        except Exception as e:
            pass

        return vulnerabilities

class ScreenshotViewer(QWidget):
    """Widget for displaying screenshots and visual evidence"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.screenshots = []
        self.current_index = 0

    def init_ui(self):
        """Initialize the screenshot viewer UI"""
        layout = QVBoxLayout(self)

        # Controls
        controls_layout = QHBoxLayout()

        self.prev_button = QPushButton("← Previous")
        self.prev_button.clicked.connect(self.show_previous)
        self.prev_button.setEnabled(False)

        self.next_button = QPushButton("Next →")
        self.next_button.clicked.connect(self.show_next)
        self.next_button.setEnabled(False)

        self.screenshot_info = QLabel("No screenshots available")
        self.screenshot_info.setAlignment(Qt.AlignCenter)

        controls_layout.addWidget(self.prev_button)
        controls_layout.addWidget(self.screenshot_info)
        controls_layout.addWidget(self.next_button)

        layout.addLayout(controls_layout)

        # Screenshot display
        self.screenshot_scroll = QScrollArea()
        self.screenshot_label = QLabel()
        self.screenshot_label.setAlignment(Qt.AlignCenter)
        self.screenshot_label.setStyleSheet("border: 1px solid #cccccc; background-color: white;")
        self.screenshot_label.setText("No screenshot to display")
        self.screenshot_label.setMinimumSize(800, 600)

        self.screenshot_scroll.setWidget(self.screenshot_label)
        self.screenshot_scroll.setWidgetResizable(True)
        layout.addWidget(self.screenshot_scroll)

        # Description
        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(100)
        self.description_text.setReadOnly(True)
        layout.addWidget(self.description_text)

    def add_screenshots(self, screenshots):
        """Add screenshots to the viewer"""
        self.screenshots.extend(screenshots)
        self.update_navigation()
        if self.screenshots and self.current_index == 0:
            self.show_screenshot(0)

    def show_screenshot(self, index):
        """Display a specific screenshot"""
        if 0 <= index < len(self.screenshots):
            self.current_index = index
            screenshot = self.screenshots[index]

            try:
                # Convert base64 to QPixmap
                image_data = base64.b64decode(screenshot['data'])
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)

                # Scale image to fit while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(
                    self.screenshot_label.size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                self.screenshot_label.setPixmap(scaled_pixmap)

                # Update info
                self.screenshot_info.setText(f"Screenshot {index + 1} of {len(self.screenshots)}")

                # Update description
                description = f"Timestamp: {screenshot['timestamp']}\n"
                description += f"URL: {screenshot['url']}\n"
                description += f"Step: {screenshot['step']}\n"
                description += f"Description: {screenshot['description']}"
                self.description_text.setPlainText(description)

            except Exception as e:
                self.screenshot_label.setText(f"Error loading screenshot: {str(e)}")

            self.update_navigation()

    def show_previous(self):
        """Show previous screenshot"""
        if self.current_index > 0:
            self.show_screenshot(self.current_index - 1)

    def show_next(self):
        """Show next screenshot"""
        if self.current_index < len(self.screenshots) - 1:
            self.show_screenshot(self.current_index + 1)

    def update_navigation(self):
        """Update navigation button states"""
        self.prev_button.setEnabled(self.current_index > 0)
        self.next_button.setEnabled(self.current_index < len(self.screenshots) - 1)

    def clear_screenshots(self):
        """Clear all screenshots"""
        self.screenshots = []
        self.current_index = 0
        self.screenshot_label.clear()
        self.screenshot_label.setText("No screenshot to display")
        self.screenshot_info.setText("No screenshots available")
        self.description_text.clear()
        self.update_navigation()

class GeminiAnalyzer:
    """Gemini AI integration for vulnerability analysis"""

    def __init__(self, api_key=None):
        self.api_key = api_key
        if api_key:
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-pro')

    def analyze_vulnerabilities(self, vulnerabilities):
        """Analyze vulnerabilities using Gemini AI"""
        if not self.api_key or not vulnerabilities:
            return "Gemini AI analysis not available (API key not configured) or no vulnerabilities found."

        try:
            # Prepare vulnerability summary for AI analysis
            vuln_summary = "Security Vulnerabilities Found:\n\n"
            for i, vuln in enumerate(vulnerabilities, 1):
                vuln_summary += f"{i}. {vuln['type']}\n"
                vuln_summary += f"   URL: {vuln.get('url', 'N/A')}\n"
                vuln_summary += f"   Evidence: {vuln.get('evidence', 'N/A')}\n"
                vuln_summary += f"   Timestamp: {vuln['timestamp']}\n\n"

            prompt = f"""
            As a cybersecurity expert, analyze the following web application vulnerabilities and provide:
            1. Risk assessment for each vulnerability (Critical/High/Medium/Low)
            2. Detailed explanation of the security implications
            3. Specific remediation steps for each issue
            4. Overall security posture assessment

            Vulnerabilities:
            {vuln_summary}

            Please provide a comprehensive security analysis report.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            return f"Error during AI analysis: {str(e)}"

class ScannerThread(QThread):
    """Worker thread for security scanning with browser integration"""

    progress_update = pyqtSignal(str)
    scan_complete = pyqtSignal(list, str)
    screenshot_update = pyqtSignal(list)

    def __init__(self, url, gemini_api_key=None, use_browser=True):
        super().__init__()
        self.url = url
        self.gemini_api_key = gemini_api_key
        self.use_browser = use_browser

    def run(self):
        """Execute security scan with browser integration"""
        scanner = SecurityScanner(use_browser=self.use_browser)
        all_vulnerabilities = []
        all_screenshots = []

        self.progress_update.emit("Starting enhanced security scan...")
        self.progress_update.emit(f"Target URL: {self.url}")
        self.progress_update.emit(f"Browser testing: {'Enabled' if self.use_browser else 'Disabled'}")
        self.progress_update.emit("=" * 50)

        # Initialize browser if enabled
        if self.use_browser:
            self.progress_update.emit("Initializing headless browser...")
            if scanner.initialize_browser_testing():
                self.progress_update.emit("Browser initialized successfully.")
            else:
                self.progress_update.emit("Browser initialization failed. Falling back to HTTP-only testing.")
                scanner.use_browser = False

        try:
            # SQL Injection Tests
            self.progress_update.emit("Testing for SQL Injection vulnerabilities...")
            if scanner.use_browser:
                sql_vulns, sql_browser_results = scanner.test_sql_injection(self.url)
                # Collect screenshots from browser results
                for result in sql_browser_results:
                    if result and 'before_screenshot' in result and result['before_screenshot']:
                        all_screenshots.append(result['before_screenshot'])
                    if result and 'after_screenshot' in result and result['after_screenshot']:
                        all_screenshots.append(result['after_screenshot'])
            else:
                sql_vulns = scanner.test_sql_injection(self.url)

            all_vulnerabilities.extend(sql_vulns)
            self.progress_update.emit(f"SQL Injection test complete. Found {len(sql_vulns)} potential issues.")

            # XSS Tests
            self.progress_update.emit("Testing for Cross-Site Scripting (XSS) vulnerabilities...")
            if scanner.use_browser:
                xss_vulns, xss_browser_results = scanner.test_xss(self.url)
                # Collect screenshots from browser results
                for result in xss_browser_results:
                    if result and 'before_screenshot' in result and result['before_screenshot']:
                        all_screenshots.append(result['before_screenshot'])
                    if result and 'after_screenshot' in result and result['after_screenshot']:
                        all_screenshots.append(result['after_screenshot'])
            else:
                xss_vulns = scanner.test_xss(self.url)

            all_vulnerabilities.extend(xss_vulns)
            self.progress_update.emit(f"XSS test complete. Found {len(xss_vulns)} potential issues.")

            # Directory Traversal Tests
            self.progress_update.emit("Testing for Directory Traversal vulnerabilities...")
            dir_vulns = scanner.test_directory_traversal(self.url)
            all_vulnerabilities.extend(dir_vulns)
            self.progress_update.emit(f"Directory Traversal test complete. Found {len(dir_vulns)} potential issues.")

            # SSL/TLS Analysis
            self.progress_update.emit("Analyzing SSL/TLS configuration...")
            ssl_vulns = scanner.analyze_ssl_tls(self.url)
            all_vulnerabilities.extend(ssl_vulns)
            self.progress_update.emit(f"SSL/TLS analysis complete. Found {len(ssl_vulns)} potential issues.")

            # Emit screenshots for display
            if all_screenshots:
                self.screenshot_update.emit(all_screenshots)

            # AI Analysis
            self.progress_update.emit("Performing AI-powered vulnerability analysis...")
            analyzer = GeminiAnalyzer(self.gemini_api_key)
            ai_analysis = analyzer.analyze_vulnerabilities(all_vulnerabilities)

            self.progress_update.emit("Scan completed!")
            self.scan_complete.emit(all_vulnerabilities, ai_analysis)

        finally:
            # Clean up browser
            if scanner.browser_manager:
                scanner.close_browser()
                self.progress_update.emit("Browser session closed.")

class StellaSecurityScanner(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.scanner_thread = None
        self.init_ui()
        self.show_disclaimer()

    def init_ui(self):
        """Initialize the user interface with tabbed layout"""
        self.setWindowTitle("Stella CyberSecurity - Enhanced Web Vulnerability Scanner")
        self.setGeometry(100, 100, 1400, 900)

        # Set grey background
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #cccccc;
                border-radius: 4px;
                font-size: 12px;
            }
            QTextEdit {
                border: 2px solid #cccccc;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#start_button {
                background-color: #4CAF50;
                color: white;
            }
            QPushButton#start_button:hover {
                background-color: #45a049;
            }
            QPushButton#clear_button {
                background-color: #f44336;
                color: white;
            }
            QPushButton#clear_button:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel("Stella CyberSecurity - Web Vulnerability Scanner")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)

        # URL input section
        url_frame = QFrame()
        url_frame.setFrameStyle(QFrame.StyledPanel)
        url_layout = QVBoxLayout(url_frame)

        url_label = QLabel("Target URL:")
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter the URL to scan (e.g., https://example.com)")

        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input)
        main_layout.addWidget(url_frame)

        # Gemini API Key input section
        api_frame = QFrame()
        api_frame.setFrameStyle(QFrame.StyledPanel)
        api_layout = QVBoxLayout(api_frame)

        api_label = QLabel("Gemini AI API Key (Optional - for AI analysis):")
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your Google Gemini AI API key for enhanced analysis")
        self.api_key_input.setEchoMode(QLineEdit.Password)

        api_layout.addWidget(api_label)
        api_layout.addWidget(self.api_key_input)
        main_layout.addWidget(api_frame)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Security Scan")
        self.start_button.setObjectName("start_button")
        self.start_button.clicked.connect(self.start_scan)

        self.clear_button = QPushButton("Clear Results")
        self.clear_button.setObjectName("clear_button")
        self.clear_button.clicked.connect(self.clear_results)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()

        main_layout.addLayout(button_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Tabbed interface for results and visual demo
        self.tab_widget = QTabWidget()

        # Results tab
        results_tab = QWidget()
        results_layout = QVBoxLayout(results_tab)

        results_label = QLabel("Scan Results:")
        results_layout.addWidget(results_label)

        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setPlainText("Ready to scan. Enter a target URL and click 'Start Security Scan'.")
        results_layout.addWidget(self.results_text)

        self.tab_widget.addTab(results_tab, "📄 Scan Results")

        # Visual Demo tab
        visual_tab = QWidget()
        visual_layout = QVBoxLayout(visual_tab)

        # Browser status
        self.browser_status = QLabel("Browser Integration Status: Checking...")
        self.browser_status.setStyleSheet("font-weight: bold; padding: 5px;")
        visual_layout.addWidget(self.browser_status)

        # Check browser availability
        if SELENIUM_AVAILABLE:
            self.browser_status.setText("✅ Browser Integration: Available (Selenium installed)")
            self.browser_status.setStyleSheet("font-weight: bold; padding: 5px; color: green;")
        else:
            self.browser_status.setText("❌ Browser Integration: Unavailable (Install selenium and webdriver-manager)")
            self.browser_status.setStyleSheet("font-weight: bold; padding: 5px; color: red;")

        # Screenshot viewer
        self.screenshot_viewer = ScreenshotViewer()
        visual_layout.addWidget(self.screenshot_viewer)

        self.tab_widget.addTab(visual_tab, "🖼️ Visual Demo")

        main_layout.addWidget(self.tab_widget)

    def show_disclaimer(self):
        """Show ethical use disclaimer"""
        disclaimer = QMessageBox()
        disclaimer.setWindowTitle("Ethical Use Disclaimer")
        disclaimer.setIcon(QMessageBox.Warning)
        disclaimer.setText(
            "IMPORTANT LEGAL AND ETHICAL NOTICE\n\n"
            "This security scanning tool should ONLY be used on:\n"
            "• Systems you own\n"
            "• Systems you have explicit written permission to test\n"
            "• Authorized penetration testing engagements\n\n"
            "Unauthorized security testing is illegal and unethical.\n"
            "Users are solely responsible for ensuring proper authorization.\n\n"
            "By clicking 'I Agree', you confirm that you will only use this tool "
            "for authorized security testing purposes."
        )
        disclaimer.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        disclaimer.button(QMessageBox.Ok).setText("I Agree")
        disclaimer.button(QMessageBox.Cancel).setText("Cancel")

        if disclaimer.exec_() != QMessageBox.Ok:
            sys.exit()

    def start_scan(self):
        """Start the security scan"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Input Error", "Please enter a target URL.")
            return

        # Basic URL validation
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
            self.url_input.setText(url)

        # Get API key
        api_key = self.api_key_input.text().strip() or None

        # Disable controls during scan
        self.start_button.setEnabled(False)
        self.url_input.setEnabled(False)
        self.api_key_input.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # Clear previous results
        self.results_text.clear()
        self.screenshot_viewer.clear_screenshots()

        # Start scanner thread with browser integration
        use_browser = SELENIUM_AVAILABLE  # Use browser if available
        self.scanner_thread = ScannerThread(url, api_key, use_browser)
        self.scanner_thread.progress_update.connect(self.update_progress)
        self.scanner_thread.scan_complete.connect(self.scan_finished)
        self.scanner_thread.screenshot_update.connect(self.update_screenshots)
        self.scanner_thread.start()

    def update_progress(self, message):
        """Update progress display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.append(f"[{timestamp}] {message}")
        self.results_text.ensureCursorVisible()

    def update_screenshots(self, screenshots):
        """Update screenshot viewer with new screenshots"""
        self.screenshot_viewer.add_screenshots(screenshots)
        # Switch to visual demo tab when screenshots are available
        self.tab_widget.setCurrentIndex(1)

    def scan_finished(self, vulnerabilities, ai_analysis):
        """Handle scan completion"""
        # Re-enable controls
        self.start_button.setEnabled(True)
        self.url_input.setEnabled(True)
        self.api_key_input.setEnabled(True)
        self.progress_bar.setVisible(False)

        # Display results summary
        self.results_text.append("\n" + "=" * 60)
        self.results_text.append("SCAN SUMMARY REPORT")
        self.results_text.append("=" * 60)
        self.results_text.append(f"Total vulnerabilities found: {len(vulnerabilities)}")
        self.results_text.append(f"Scan completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if vulnerabilities:
            self.results_text.append("\nDETAILED VULNERABILITY REPORT:")
            self.results_text.append("-" * 40)

            for i, vuln in enumerate(vulnerabilities, 1):
                self.results_text.append(f"\n{i}. {vuln['type']}")
                self.results_text.append(f"   URL: {vuln.get('url', 'N/A')}")
                self.results_text.append(f"   Evidence: {vuln.get('evidence', 'N/A')}")
                self.results_text.append(f"   Timestamp: {vuln['timestamp']}")
                if 'payload' in vuln:
                    self.results_text.append(f"   Payload: {vuln['payload']}")

                # Add visual evidence information for browser-detected vulnerabilities
                if 'visual_evidence' in vuln:
                    self.results_text.append("   📸 Visual Evidence: Available in Visual Demo tab")
                    if 'form_action' in vuln:
                        self.results_text.append(f"   Form Action: {vuln['form_action']}")
                    if 'form_method' in vuln:
                        self.results_text.append(f"   Form Method: {vuln['form_method']}")
        else:
            self.results_text.append("\nNo vulnerabilities detected in this scan.")
            self.results_text.append("Note: This does not guarantee the application is secure.")
            self.results_text.append("Consider running additional security tests and manual reviews.")

        # Display AI analysis
        if ai_analysis and "not available" not in ai_analysis.lower():
            self.results_text.append("\n" + "=" * 60)
            self.results_text.append("GEMINI AI SECURITY ANALYSIS")
            self.results_text.append("=" * 60)
            self.results_text.append(ai_analysis)
        elif vulnerabilities:
            self.results_text.append("\n" + "=" * 60)
            self.results_text.append("AI ANALYSIS NOT AVAILABLE")
            self.results_text.append("=" * 60)
            self.results_text.append("To get detailed AI-powered analysis and remediation recommendations,")
            self.results_text.append("please provide a valid Gemini AI API key.")

        self.results_text.ensureCursorVisible()

    def clear_results(self):
        """Clear the results area and screenshots"""
        self.results_text.clear()
        self.results_text.setPlainText("Ready to scan. Enter a target URL and click 'Start Security Scan'.")
        self.screenshot_viewer.clear_screenshots()
        # Switch back to results tab
        self.tab_widget.setCurrentIndex(0)

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Stella CyberSecurity Scanner")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Stella CyberSecurity")

    # Create and show main window
    window = StellaSecurityScanner()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
