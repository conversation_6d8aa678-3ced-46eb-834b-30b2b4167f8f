#!/usr/bin/env python3
"""
Stella CyberSecurity - Web Vulnerability Scanner with AI Analysis
A PyQt5 desktop application for automated web security testing with Gemini AI integration.

IMPORTANT: This tool should only be used for authorized security testing on systems
you own or have explicit permission to test. Unauthorized testing is illegal and unethical.
"""

import sys
import time
import threading
import ssl
import socket
import urllib.parse
import requests
import json
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QLineEdit, QTextEdit, QPushButton,
                            QProgressBar, QMessageBox, QFrame, QSplitter)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPalette, QColor
import google.generativeai as genai

class SecurityScanner:
    """Core security scanning functionality"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Stella-Security-Scanner/1.0 (Authorized Testing)'
        })
        self.vulnerabilities = []

    def test_sql_injection(self, url):
        """Test for SQL injection vulnerabilities"""
        payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' OR 'x'='x",
            "1' OR '1'='1' /*"
        ]

        vulnerabilities = []
        for payload in payloads:
            try:
                # Test GET parameters
                test_url = f"{url}?id={payload}"
                response = self.session.get(test_url, timeout=10)

                # Look for SQL error indicators
                error_indicators = [
                    "mysql_fetch", "ORA-", "Microsoft OLE DB", "ODBC SQL Server",
                    "PostgreSQL", "SQLite", "syntax error", "mysql_num_rows"
                ]

                for indicator in error_indicators:
                    if indicator.lower() in response.text.lower():
                        vulnerabilities.append({
                            'type': 'SQL Injection',
                            'payload': payload,
                            'url': test_url,
                            'evidence': indicator,
                            'timestamp': datetime.now().isoformat()
                        })
                        break

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                continue

        return vulnerabilities

    def test_xss(self, url):
        """Test for Cross-Site Scripting vulnerabilities"""
        payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//",
            "<svg onload=alert('XSS')>",
            "\"onmouseover=\"alert('XSS')\""
        ]

        vulnerabilities = []
        for payload in payloads:
            try:
                # Test GET parameters
                test_url = f"{url}?search={urllib.parse.quote(payload)}"
                response = self.session.get(test_url, timeout=10)

                # Check if payload is reflected in response
                if payload in response.text or urllib.parse.quote(payload) in response.text:
                    vulnerabilities.append({
                        'type': 'Cross-Site Scripting (XSS)',
                        'payload': payload,
                        'url': test_url,
                        'evidence': 'Payload reflected in response',
                        'timestamp': datetime.now().isoformat()
                    })

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                continue

        return vulnerabilities

    def test_directory_traversal(self, url):
        """Test for directory traversal vulnerabilities"""
        payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd"
        ]

        vulnerabilities = []
        for payload in payloads:
            try:
                test_url = f"{url}?file={payload}"
                response = self.session.get(test_url, timeout=10)

                # Look for file content indicators
                indicators = ["root:", "daemon:", "bin:", "[boot loader]", "127.0.0.1"]

                for indicator in indicators:
                    if indicator in response.text:
                        vulnerabilities.append({
                            'type': 'Directory Traversal',
                            'payload': payload,
                            'url': test_url,
                            'evidence': f'Found indicator: {indicator}',
                            'timestamp': datetime.now().isoformat()
                        })
                        break

                time.sleep(0.5)  # Rate limiting

            except Exception as e:
                continue

        return vulnerabilities

    def analyze_ssl_tls(self, url):
        """Analyze SSL/TLS configuration"""
        vulnerabilities = []
        try:
            parsed_url = urllib.parse.urlparse(url)
            hostname = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)

            if parsed_url.scheme == 'https':
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cert = ssock.getpeercert()
                        cipher = ssock.cipher()

                        # Check for weak ciphers
                        weak_ciphers = ['RC4', 'DES', 'MD5']
                        if any(weak in cipher[0] for weak in weak_ciphers):
                            vulnerabilities.append({
                                'type': 'Weak SSL/TLS Cipher',
                                'evidence': f'Weak cipher detected: {cipher[0]}',
                                'url': url,
                                'timestamp': datetime.now().isoformat()
                            })

                        # Check certificate expiration
                        not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                        days_until_expiry = (not_after - datetime.now()).days

                        if days_until_expiry < 30:
                            vulnerabilities.append({
                                'type': 'SSL Certificate Expiring Soon',
                                'evidence': f'Certificate expires in {days_until_expiry} days',
                                'url': url,
                                'timestamp': datetime.now().isoformat()
                            })

        except Exception as e:
            pass

        return vulnerabilities

class GeminiAnalyzer:
    """Gemini AI integration for vulnerability analysis"""

    def __init__(self, api_key=None):
        self.api_key = api_key
        if api_key:
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-pro')

    def analyze_vulnerabilities(self, vulnerabilities):
        """Analyze vulnerabilities using Gemini AI"""
        if not self.api_key or not vulnerabilities:
            return "Gemini AI analysis not available (API key not configured) or no vulnerabilities found."

        try:
            # Prepare vulnerability summary for AI analysis
            vuln_summary = "Security Vulnerabilities Found:\n\n"
            for i, vuln in enumerate(vulnerabilities, 1):
                vuln_summary += f"{i}. {vuln['type']}\n"
                vuln_summary += f"   URL: {vuln.get('url', 'N/A')}\n"
                vuln_summary += f"   Evidence: {vuln.get('evidence', 'N/A')}\n"
                vuln_summary += f"   Timestamp: {vuln['timestamp']}\n\n"

            prompt = f"""
            As a cybersecurity expert, analyze the following web application vulnerabilities and provide:
            1. Risk assessment for each vulnerability (Critical/High/Medium/Low)
            2. Detailed explanation of the security implications
            3. Specific remediation steps for each issue
            4. Overall security posture assessment

            Vulnerabilities:
            {vuln_summary}

            Please provide a comprehensive security analysis report.
            """

            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            return f"Error during AI analysis: {str(e)}"

class ScannerThread(QThread):
    """Worker thread for security scanning"""

    progress_update = pyqtSignal(str)
    scan_complete = pyqtSignal(list, str)

    def __init__(self, url, gemini_api_key=None):
        super().__init__()
        self.url = url
        self.gemini_api_key = gemini_api_key

    def run(self):
        """Execute security scan"""
        scanner = SecurityScanner()
        all_vulnerabilities = []

        self.progress_update.emit("Starting security scan...")
        self.progress_update.emit(f"Target URL: {self.url}")
        self.progress_update.emit("=" * 50)

        # SQL Injection Tests
        self.progress_update.emit("Testing for SQL Injection vulnerabilities...")
        sql_vulns = scanner.test_sql_injection(self.url)
        all_vulnerabilities.extend(sql_vulns)
        self.progress_update.emit(f"SQL Injection test complete. Found {len(sql_vulns)} potential issues.")

        # XSS Tests
        self.progress_update.emit("Testing for Cross-Site Scripting (XSS) vulnerabilities...")
        xss_vulns = scanner.test_xss(self.url)
        all_vulnerabilities.extend(xss_vulns)
        self.progress_update.emit(f"XSS test complete. Found {len(xss_vulns)} potential issues.")

        # Directory Traversal Tests
        self.progress_update.emit("Testing for Directory Traversal vulnerabilities...")
        dir_vulns = scanner.test_directory_traversal(self.url)
        all_vulnerabilities.extend(dir_vulns)
        self.progress_update.emit(f"Directory Traversal test complete. Found {len(dir_vulns)} potential issues.")

        # SSL/TLS Analysis
        self.progress_update.emit("Analyzing SSL/TLS configuration...")
        ssl_vulns = scanner.analyze_ssl_tls(self.url)
        all_vulnerabilities.extend(ssl_vulns)
        self.progress_update.emit(f"SSL/TLS analysis complete. Found {len(ssl_vulns)} potential issues.")

        # AI Analysis
        self.progress_update.emit("Performing AI-powered vulnerability analysis...")
        analyzer = GeminiAnalyzer(self.gemini_api_key)
        ai_analysis = analyzer.analyze_vulnerabilities(all_vulnerabilities)

        self.progress_update.emit("Scan completed!")
        self.scan_complete.emit(all_vulnerabilities, ai_analysis)

class StellaSecurityScanner(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.scanner_thread = None
        self.init_ui()
        self.show_disclaimer()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Stella CyberSecurity - Web Vulnerability Scanner")
        self.setGeometry(100, 100, 1000, 700)

        # Set grey background
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #cccccc;
                border-radius: 4px;
                font-size: 12px;
            }
            QTextEdit {
                border: 2px solid #cccccc;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#start_button {
                background-color: #4CAF50;
                color: white;
            }
            QPushButton#start_button:hover {
                background-color: #45a049;
            }
            QPushButton#clear_button {
                background-color: #f44336;
                color: white;
            }
            QPushButton#clear_button:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel("Stella CyberSecurity - Web Vulnerability Scanner")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)

        # URL input section
        url_frame = QFrame()
        url_frame.setFrameStyle(QFrame.StyledPanel)
        url_layout = QVBoxLayout(url_frame)

        url_label = QLabel("Target URL:")
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter the URL to scan (e.g., https://example.com)")

        url_layout.addWidget(url_label)
        url_layout.addWidget(self.url_input)
        main_layout.addWidget(url_frame)

        # Gemini API Key input section
        api_frame = QFrame()
        api_frame.setFrameStyle(QFrame.StyledPanel)
        api_layout = QVBoxLayout(api_frame)

        api_label = QLabel("Gemini AI API Key (Optional - for AI analysis):")
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your Google Gemini AI API key for enhanced analysis")
        self.api_key_input.setEchoMode(QLineEdit.Password)

        api_layout.addWidget(api_label)
        api_layout.addWidget(self.api_key_input)
        main_layout.addWidget(api_frame)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("Start Security Scan")
        self.start_button.setObjectName("start_button")
        self.start_button.clicked.connect(self.start_scan)

        self.clear_button = QPushButton("Clear Results")
        self.clear_button.setObjectName("clear_button")
        self.clear_button.clicked.connect(self.clear_results)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()

        main_layout.addLayout(button_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Results area
        results_label = QLabel("Scan Results:")
        main_layout.addWidget(results_label)

        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setPlainText("Ready to scan. Enter a target URL and click 'Start Security Scan'.")
        main_layout.addWidget(self.results_text)

    def show_disclaimer(self):
        """Show ethical use disclaimer"""
        disclaimer = QMessageBox()
        disclaimer.setWindowTitle("Ethical Use Disclaimer")
        disclaimer.setIcon(QMessageBox.Warning)
        disclaimer.setText(
            "IMPORTANT LEGAL AND ETHICAL NOTICE\n\n"
            "This security scanning tool should ONLY be used on:\n"
            "• Systems you own\n"
            "• Systems you have explicit written permission to test\n"
            "• Authorized penetration testing engagements\n\n"
            "Unauthorized security testing is illegal and unethical.\n"
            "Users are solely responsible for ensuring proper authorization.\n\n"
            "By clicking 'I Agree', you confirm that you will only use this tool "
            "for authorized security testing purposes."
        )
        disclaimer.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        disclaimer.button(QMessageBox.Ok).setText("I Agree")
        disclaimer.button(QMessageBox.Cancel).setText("Cancel")

        if disclaimer.exec_() != QMessageBox.Ok:
            sys.exit()

    def start_scan(self):
        """Start the security scan"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Input Error", "Please enter a target URL.")
            return

        # Basic URL validation
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
            self.url_input.setText(url)

        # Get API key
        api_key = self.api_key_input.text().strip() or None

        # Disable controls during scan
        self.start_button.setEnabled(False)
        self.url_input.setEnabled(False)
        self.api_key_input.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # Clear previous results
        self.results_text.clear()

        # Start scanner thread
        self.scanner_thread = ScannerThread(url, api_key)
        self.scanner_thread.progress_update.connect(self.update_progress)
        self.scanner_thread.scan_complete.connect(self.scan_finished)
        self.scanner_thread.start()

    def update_progress(self, message):
        """Update progress display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.append(f"[{timestamp}] {message}")
        self.results_text.ensureCursorVisible()

    def scan_finished(self, vulnerabilities, ai_analysis):
        """Handle scan completion"""
        # Re-enable controls
        self.start_button.setEnabled(True)
        self.url_input.setEnabled(True)
        self.api_key_input.setEnabled(True)
        self.progress_bar.setVisible(False)

        # Display results summary
        self.results_text.append("\n" + "=" * 60)
        self.results_text.append("SCAN SUMMARY REPORT")
        self.results_text.append("=" * 60)
        self.results_text.append(f"Total vulnerabilities found: {len(vulnerabilities)}")
        self.results_text.append(f"Scan completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if vulnerabilities:
            self.results_text.append("\nDETAILED VULNERABILITY REPORT:")
            self.results_text.append("-" * 40)

            for i, vuln in enumerate(vulnerabilities, 1):
                self.results_text.append(f"\n{i}. {vuln['type']}")
                self.results_text.append(f"   URL: {vuln.get('url', 'N/A')}")
                self.results_text.append(f"   Evidence: {vuln.get('evidence', 'N/A')}")
                self.results_text.append(f"   Timestamp: {vuln['timestamp']}")
                if 'payload' in vuln:
                    self.results_text.append(f"   Payload: {vuln['payload']}")
        else:
            self.results_text.append("\nNo vulnerabilities detected in this scan.")
            self.results_text.append("Note: This does not guarantee the application is secure.")
            self.results_text.append("Consider running additional security tests and manual reviews.")

        # Display AI analysis
        if ai_analysis and "not available" not in ai_analysis.lower():
            self.results_text.append("\n" + "=" * 60)
            self.results_text.append("GEMINI AI SECURITY ANALYSIS")
            self.results_text.append("=" * 60)
            self.results_text.append(ai_analysis)
        elif vulnerabilities:
            self.results_text.append("\n" + "=" * 60)
            self.results_text.append("AI ANALYSIS NOT AVAILABLE")
            self.results_text.append("=" * 60)
            self.results_text.append("To get detailed AI-powered analysis and remediation recommendations,")
            self.results_text.append("please provide a valid Gemini AI API key.")

        self.results_text.ensureCursorVisible()

    def clear_results(self):
        """Clear the results area"""
        self.results_text.clear()
        self.results_text.setPlainText("Ready to scan. Enter a target URL and click 'Start Security Scan'.")

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Stella CyberSecurity Scanner")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Stella CyberSecurity")

    # Create and show main window
    window = StellaSecurityScanner()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
