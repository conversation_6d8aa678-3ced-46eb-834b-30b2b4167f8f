#!/usr/bin/env python3
"""
Test script for Stella CyberSecurity Scanner
This script tests the basic functionality without requiring a full GUI.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import PyQt5
        print("✅ PyQt5 imported successfully")
    except ImportError as e:
        print(f"❌ PyQt5 import failed: {e}")
        return False
    
    try:
        import requests
        print("✅ requests imported successfully")
    except ImportError as e:
        print(f"❌ requests import failed: {e}")
        return False
    
    try:
        import google.generativeai
        print("✅ google-generativeai imported successfully")
    except ImportError as e:
        print(f"❌ google-generativeai import failed: {e}")
        return False
    
    try:
        from selenium import webdriver
        print("✅ selenium imported successfully")
    except ImportError as e:
        print(f"⚠️ selenium import failed: {e}")
        print("   Browser integration will be disabled")
    
    try:
        from PIL import Image
        print("✅ Pillow imported successfully")
    except ImportError as e:
        print(f"⚠️ Pillow import failed: {e}")
        print("   Image processing will be limited")
    
    return True

def test_scanner_classes():
    """Test if scanner classes can be instantiated"""
    print("\nTesting scanner classes...")
    
    try:
        from stella_security_scanner import SecurityScanner, GeminiAnalyzer, BrowserManager
        
        # Test SecurityScanner
        scanner = SecurityScanner(use_browser=False)
        print("✅ SecurityScanner instantiated successfully")
        
        # Test GeminiAnalyzer
        analyzer = GeminiAnalyzer()
        print("✅ GeminiAnalyzer instantiated successfully")
        
        # Test BrowserManager
        browser_manager = BrowserManager()
        print("✅ BrowserManager instantiated successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Scanner class test failed: {e}")
        return False

def test_basic_functionality():
    """Test basic scanning functionality"""
    print("\nTesting basic functionality...")
    
    try:
        from stella_security_scanner import SecurityScanner
        
        scanner = SecurityScanner(use_browser=False)
        
        # Test a simple HTTP request (using a safe test URL)
        test_url = "https://httpbin.org/get"
        print(f"Testing with URL: {test_url}")
        
        # Test directory traversal (should not find anything on httpbin)
        dir_vulns = scanner.test_directory_traversal(test_url)
        print(f"✅ Directory traversal test completed: {len(dir_vulns)} results")
        
        # Test SSL analysis
        ssl_vulns = scanner.analyze_ssl_tls(test_url)
        print(f"✅ SSL/TLS analysis completed: {len(ssl_vulns)} results")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Stella CyberSecurity Scanner - Test Suite")
    print("=" * 50)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test scanner classes
    if not test_scanner_classes():
        all_passed = False
    
    # Test basic functionality
    if not test_basic_functionality():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All tests passed! The application should work correctly.")
        print("\nTo run the full application:")
        print("python stella_security_scanner.py")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        print("\nTo install missing dependencies:")
        print("pip install -r requirements.txt")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
