# Stella CyberSecurity - Installation & Setup Guide

## Quick Start

### 1. Install Dependencies

First, install all required Python packages:

```bash
pip install -r requirements.txt
```

If you encounter issues, install packages individually:

```bash
pip install PyQt5>=5.15.0
pip install requests>=2.25.0
pip install google-generativeai>=0.3.0
pip install selenium>=4.0.0
pip install webdriver-manager>=3.8.0
pip install Pillow>=8.0.0
```

### 2. Browser Setup (Optional but Recommended)

The application will automatically download and manage browser drivers when first used. Ensure you have either:

- **Chrome/Chromium** (recommended)
- **Firefox**

### 3. Run the Application

```bash
python stella_security_scanner.py
```

## Features Overview

### 🔍 Enhanced Security Testing

The application now includes two modes of testing:

1. **Traditional HTTP Testing**: Basic vulnerability scanning using HTTP requests
2. **Browser-Based Testing**: Interactive testing with visual demonstration

### 🖼️ Visual Demonstration Features

- **Real-time Screenshots**: See exactly what the scanner is doing
- **Before/After Comparisons**: Visual evidence of vulnerability exploitation
- **Form Interaction**: Watch the scanner automatically find and test web forms
- **Step-by-step Documentation**: Educational view of testing process

### 📱 User Interface

The application features a tabbed interface:

1. **📄 Scan Results Tab**: Traditional text-based results and analysis
2. **🖼️ Visual Demo Tab**: Screenshot viewer with navigation controls

## Usage Instructions

### Basic Scanning

1. **Accept the Ethical Use Disclaimer**
2. **Enter Target URL**: Input the website you want to test
3. **Enter Gemini AI API Key** (optional): For enhanced AI analysis
4. **Click "Start Security Scan"**

### Visual Demonstration Mode

When browser integration is available:

1. The scanner will automatically initialize a headless browser
2. Navigate to the target website
3. Find and interact with web forms
4. Test various vulnerability payloads
5. Capture screenshots of the testing process
6. Display visual evidence in the Visual Demo tab

### Viewing Results

- **Scan Results Tab**: View detailed vulnerability reports
- **Visual Demo Tab**: Browse screenshots with navigation controls
- **Screenshot Navigation**: Use Previous/Next buttons to view all captured evidence

## Browser Integration Status

The application will display the browser integration status:

- ✅ **Available**: Selenium is installed and browser drivers can be managed automatically
- ❌ **Unavailable**: Missing dependencies - install selenium and webdriver-manager

## Troubleshooting

### Common Issues

1. **"Module not found" errors**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **Browser driver issues**
   - The application automatically downloads drivers
   - Ensure you have Chrome or Firefox installed
   - Check your internet connection for driver downloads

3. **PyQt5 installation issues on Linux**
   ```bash
   sudo apt-get install python3-pyqt5
   ```

4. **Permission errors**
   - Run with appropriate user permissions
   - Check antivirus software isn't blocking the application

### Testing the Installation

Run the test script to verify everything is working:

```bash
python test_app.py
```

This will check:
- All required modules can be imported
- Scanner classes can be instantiated
- Basic functionality works correctly

## Security Considerations

### Ethical Use

- **Only test systems you own or have explicit permission to test**
- **Unauthorized testing is illegal and unethical**
- **Always follow responsible disclosure practices**

### Rate Limiting

The scanner includes built-in rate limiting to:
- Avoid overwhelming target servers
- Prevent detection by security systems
- Ensure responsible testing practices

### Data Privacy

- Screenshots are stored temporarily in memory only
- No data is transmitted to external services (except Gemini AI if configured)
- Clear results to remove all captured data

## Advanced Configuration

### Browser Options

The scanner uses headless browsers by default. You can modify the browser settings in the `BrowserManager` class:

- Change browser type (Chrome/Firefox)
- Modify window size for screenshots
- Adjust timeout settings
- Enable/disable headless mode for debugging

### Customizing Tests

You can extend the scanner by:
- Adding new vulnerability test methods
- Modifying payload lists
- Implementing additional screenshot capture points
- Enhancing the AI analysis prompts

## Support

For technical issues:
1. Check the troubleshooting section above
2. Verify all dependencies are installed correctly
3. Test with the provided test script
4. Ensure you have proper authorization for testing

## Legal Disclaimer

This tool is provided for educational and authorized security testing purposes only. Users are solely responsible for ensuring they have proper authorization before testing any systems. The developers assume no liability for misuse of this tool.
